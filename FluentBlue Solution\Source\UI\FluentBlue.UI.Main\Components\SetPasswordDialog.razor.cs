﻿using FluentBlue.UI.Main.Shared;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main.Components
{
    public partial class SetPasswordDialog
    {
        [CascadingParameter]
        public FluentDialog Dialog { get; set; } = default!;

        public SetPasswordContent Content { get; set; } = new SetPasswordContent();

        string validationMessages = string.Empty;


        protected override void OnInitialized()
        {

        }

        private async Task Save()
        {
            string tempValidationMessages = string.Empty;

            if (Validate(out tempValidationMessages) == true)
            {
                await Dialog.CloseAsync(Content.Password);
            }
            else
            {
                this.validationMessages = tempValidationMessages;
            }
        }

        private async Task Cancel()
        {
            await Dialog.CancelAsync();
        }

        private bool Validate(out string validationMessages)
        {
            List<string> validationMessagesList = new List<string>();

            if (!System.Text.RegularExpressions.Regex.IsMatch(Content.Password, @"^(?=.*[a-z]).{1,}$"))
                validationMessagesList.Add(Resources.SetPasswordDialogResource.LowerCaseLetterMessage);
            if (!System.Text.RegularExpressions.Regex.IsMatch(Content.Password, @"^(?=.*[A-Z]).{1,}$"))
                validationMessagesList.Add(Resources.SetPasswordDialogResource.UpperCaseLetterMessage);
            if (!System.Text.RegularExpressions.Regex.IsMatch(Content.Password, @"^(?=.*[~!@\#$%^&*()]).{1,}$"))
                validationMessagesList.Add(Resources.SetPasswordDialogResource.NonAlphanumericCharacterMessage);
            if (!System.Text.RegularExpressions.Regex.IsMatch(Content.Password, @"^(?=.*\d).{1,}$"))
                validationMessagesList.Add(Resources.SetPasswordDialogResource.NumberMessage);

            if (Content.Password != Content.VerifyPassword)
            {
                validationMessagesList.Add(Resources.SetPasswordDialogResource.PasswordsNotMatchMessage);
            }

            validationMessages = string.Join(" <br/>", validationMessagesList);

            return validationMessagesList.Count == 0;
        }

        private void PasswordOnValueChanged(string value)
        {
            try
            {
                string tempValidationMessages = string.Empty;
                this.Content.Password = value;
                Validate(out tempValidationMessages);
                this.validationMessages = tempValidationMessages;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public class SetPasswordContent
        {
            [Required]
            [MaxLength(20)]
            public string Password { get; set; } = string.Empty;

            [Required]
            [MaxLength(20)]
            public string VerifyPassword { get; set; } = string.Empty;
        }
    }
}

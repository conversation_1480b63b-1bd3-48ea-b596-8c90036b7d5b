﻿using Azure;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Client
{
    public class EventsWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<EventsWebApiClient> logger;

        public EventsWebApiClient(HttpClient httpClient, ILogger<EventsWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Calendar.Event>> GetEvents(Guid tenantId, string filter, Guid[]? userIds, DateTime startDate, DateTime endDate)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Events/Get";

                RequestEventsParameters requestEventsParams = new RequestEventsParameters();
                requestEventsParams.TenantId = tenantId;
                requestEventsParams.Filter = filter.Trim();
                requestEventsParams.UserIds = userIds;
                requestEventsParams.StartDateTicksUtc = startDate.Ticks;
                requestEventsParams.EndDateTicksUtc = endDate.Ticks;

                string getEntitiesParamsJson = JsonConvert.SerializeObject(requestEventsParams);  //Μετατρέπουμε τις παραμέτρους για το διάβασμα entities σε json.

                //Ετοιμάζουμε το HttpContent με τα json data.
                HttpContent httpContent = new StringContent(getEntitiesParamsJson, Encoding.UTF8, "application/json");

                string urlParameters = UrlHelpers.ToQueryString(requestEventsParams, "&");
                requestUri = requestUri + "?" + urlParameters;

                //Εκτελούμε το GET request.
                //HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContent);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Model.DBOs.Calendar.Event>> response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Model.DBOs.Calendar.Event>>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetEvents({@tenantId},{fiter})", tenantId, filter);
                throw;
            }
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Calendar.Event>> GetAllEventsOfContact(Guid contactId)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Contact/Events/GetAll?ContactId=" + contactId.ToString();

                //Εκτελούμε το GET request.
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Model.DBOs.Calendar.Event>>? response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Model.DBOs.Calendar.Event>>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return new List<FluentBlue.Data.Model.DBOs.Calendar.Event>();
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetAllEventsOfContact({@contactId})", contactId);
                throw;
            }
        }

        public async Task<Event?> GetEvent(Guid eventId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/Event/Get?eventId=" + eventId.ToString();

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<Event> response = JsonConvert.DeserializeObject<ApiResponse<Event>>(responseString)!;
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetEvent({eventId})", eventId);
                throw;
            }
        }

        public async Task CreateEvent(Event eventObj, List<DateTime>? recurrenceDates)
        {
            string requestUri = httpClient.BaseAddress + apiVersion + "/" + "Event/Create";

            SaveEventRequest saveEventRequest = new SaveEventRequest
            {
                Event = eventObj,
                RecurrentEventUpdateType = null,
                RecurrenceDates = recurrenceDates
            };

            string eventJson = JsonConvert.SerializeObject(saveEventRequest);  //Μετατρέπουμε το Event object σε json.

            //Ετοιμάζουμε το HttpContext με τα json data.
            HttpContent httpContext = new StringContent(eventJson, Encoding.UTF8, "application/json");

            //Εκτελούμε το POST request.
            HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse

            string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
            ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

            if (response.ResultCode == ApiResponseResultCode.Ok)
            {
                return;
            }
            else if (response.ResultCode == ApiResponseResultCode.Exception)
            {
                this.logger.LogError(response.ExceptionMessage, "Error in CreateEvent({@event})", eventObj);
                throw new Exception(response.ExceptionMessage);
            }

            return;
        }

        public async Task<Event?> UpdateEvent(Event eventObj, RecurrentEventHandlingType? recurrentEventUpdateType, List<DateTime>? recurrenceDates)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Event/Update";

                SaveEventRequest saveEventRequest = new SaveEventRequest
                {
                    Event = eventObj,
                    RecurrentEventUpdateType = recurrentEventUpdateType,
                    RecurrenceDates = recurrenceDates
                };

                string eventJson = System.Text.Json.JsonSerializer.Serialize(saveEventRequest);  //Μετατρέπουμε το Event object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(eventJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<Event> response = JsonConvert.DeserializeObject<ApiResponse<Event>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (responseString.Contains("validation errors"))
                {
                    throw new ApplicationException("Validation errors in server");
                }
                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }
                else if (response.ResultCode == ApiResponseResultCode.DbConcurrencyException)
                {
                    ApplicationException appException = new ApplicationException(response.ExceptionMessage ?? "");
                    appException.Data.Add("DbConcurrencyException", null);
                    throw appException;
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateEvent({@eventObj})", eventObj);
                throw;
            }
        }

        public async Task<Event?> CreateOrUpdateEventBatch(List<Event> events)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Events/CreateOrUpdateBatch";

                string eventJson = System.Text.Json.JsonSerializer.Serialize(events);  //Μετατρέπουμε το Event object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(eventJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<Event> response = JsonConvert.DeserializeObject<ApiResponse<Event>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateEvent({@eventObj})", events);
                throw;
            }
        }


        public async Task DeleteEvent(Guid eventId, RecurrentEventHandlingType? recurrentEventUpdateType)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/Event/Delete";

                DeleteEventRequest deleteEventRequest = new DeleteEventRequest
                {
                    EventId = eventId,
                    RecurrentEventUpdateType = recurrentEventUpdateType
                };

                string eventJson = System.Text.Json.JsonSerializer.Serialize(deleteEventRequest);  //Μετατρέπουμε το Event object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(eventJson, Encoding.UTF8, "application/json");

                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString)!;
                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in DeleteEvent({@eventId})", eventId);
                throw;
            }
        }

        /// <summary>
        /// Retrieves events for export with all related data (Contact, EventCategory, EventState).
        /// </summary>
        /// <param name="eventIds">List of event IDs to retrieve for export</param>
        /// <returns>List of events with related data populated</returns>
        public async Task<List<Event>> GetEventsForExport(List<Guid> eventIds)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Events/GetForExport";

                ExportEventsRequest exportRequest = new ExportEventsRequest
                {
                    EventIds = eventIds
                };

                string requestJson = JsonConvert.SerializeObject(exportRequest);

                //Prepare HttpContent with json data
                HttpContent httpContent = new StringContent(requestJson, Encoding.UTF8, "application/json");

                //Execute POST request
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContent);
                string responseString = await httpResponse.Content.ReadAsStringAsync();
                ApiResponse<List<Event>> response = JsonConvert.DeserializeObject<ApiResponse<List<Event>>>(responseString)!;

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent ?? new List<Event>();
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return new List<Event>();
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetEventsForExport({@eventIds})", eventIds);
                throw;
            }
        }
    }
}

.visible {
  visibility: visible !important
}

.static {
  position: static !important
}

.fixed {
  position: fixed !important
}

.relative {
  position: relative !important
}

.bottom-0 {
  bottom: 0px !important
}

.left-0 {
  left: 0px !important
}

.top-0 {
  top: 0px !important
}

.z-50 {
  z-index: 50 !important
}

.mx-4 {
  margin-left: 1rem !important;
  margin-right: 1rem !important
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important
}

.mb-1 {
  margin-bottom: 0.25rem !important
}

.mb-2 {
  margin-bottom: 0.5rem !important
}

.mb-4 {
  margin-bottom: 1rem !important
}

.mr-2 {
  margin-right: 0.5rem !important
}

.mr-3 {
  margin-right: 0.75rem !important
}

.mt-0 {
  margin-top: 0px !important
}

.mt-1 {
  margin-top: 0.25rem !important
}

.mt-2 {
  margin-top: 0.5rem !important
}

.mt-4 {
  margin-top: 1rem !important
}

.mt-auto {
  margin-top: auto !important
}

.block {
  display: block !important
}

.inline-block {
  display: inline-block !important
}

.flex {
  display: flex !important
}

.table {
  display: table !important
}

.hidden {
  display: none !important
}

.h-10 {
  height: 2.5rem !important
}

.h-14 {
  height: 3.5rem !important
}

.h-28 {
  height: 7rem !important
}

.h-3 {
  height: 0.75rem !important
}

.h-4 {
  height: 1rem !important
}

.h-8 {
  height: 2rem !important
}

.h-96 {
  height: 24rem !important
}

.h-full {
  height: 100% !important
}

.h-px {
  height: 1px !important
}

.max-h-full {
  max-height: 100% !important
}

.min-h-64 {
  min-height: 16rem !important
}

.min-h-full {
  min-height: 100% !important
}

.min-h-screen {
  min-height: 100vh !important
}

.w-1\/2 {
  width: 50% !important
}

.w-10 {
  width: 2.5rem !important
}

.w-12 {
  width: 3rem !important
}

.w-20 {
  width: 5rem !important
}

.w-32 {
  width: 8rem !important
}

.w-40 {
  width: 10rem !important
}

.w-48 {
  width: 12rem !important
}

.w-64 {
  width: 16rem !important
}

.w-8 {
  width: 2rem !important
}

.w-fit {
  width: -moz-fit-content !important;
  width: fit-content !important
}

.w-full {
  width: 100% !important
}

.max-w-80 {
  max-width: 20rem !important
}

.max-w-96 {
  max-width: 24rem !important
}

.max-w-lg {
  max-width: 32rem !important
}

.flex-1 {
  flex: 1 1 0% !important
}

.flex-auto {
  flex: 1 1 auto !important
}

.flex-none {
  flex: none !important
}

.border-collapse {
  border-collapse: collapse !important
}

.cursor-move {
  cursor: move !important
}

.flex-col {
  flex-direction: column !important
}

.flex-wrap {
  flex-wrap: wrap !important
}

.content-center {
  align-content: center !important
}

.items-center {
  align-items: center !important
}

.justify-start {
  justify-content: flex-start !important
}

.justify-center {
  justify-content: center !important
}

.gap-2 {
  gap: 0.5rem !important
}

.self-end {
  align-self: flex-end !important
}

.overflow-hidden {
  overflow: hidden !important
}

.overflow-y-auto {
  overflow-y: auto !important
}

.overflow-x-hidden {
  overflow-x: hidden !important
}

.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important
}

.whitespace-nowrap {
  white-space: nowrap !important
}

.rounded {
  border-radius: 0.25rem !important
}

.border {
  border-width: 1px !important
}

.border-x-0 {
  border-left-width: 0px !important;
  border-right-width: 0px !important
}

.border-b-0 {
  border-bottom-width: 0px !important
}

.border-gray-300 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(209 213 219 / var(--tw-border-opacity)) !important
}

.bg-amber-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity)) !important
}

.bg-red-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity)) !important
}

.bg-transparent {
  background-color: transparent !important
}

.p-0 {
  padding: 0px !important
}

.p-1 {
  padding: 0.25rem !important
}

.p-2 {
  padding: 0.5rem !important
}

.p-4 {
  padding: 1rem !important
}

.px-0 {
  padding-left: 0px !important;
  padding-right: 0px !important
}

.px-3 {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important
}

.py-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important
}

.py-5 {
  padding-top: 1.25rem !important;
  padding-bottom: 1.25rem !important
}

.pb-3 {
  padding-bottom: 0.75rem !important
}

.ps-0 {
  padding-inline-start: 0px !important
}

.pt-3 {
  padding-top: 0.75rem !important
}

.text-left {
  text-align: left !important
}

.text-center {
  text-align: center !important
}

.text-start {
  text-align: start !important
}

.align-middle {
  vertical-align: middle !important
}

.font-bold {
  font-weight: 700 !important
}

.text-amber-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(217 119 6 / var(--tw-text-opacity)) !important
}

.text-amber-700 {
  --tw-text-opacity: 1 !important;
  color: rgb(180 83 9 / var(--tw-text-opacity)) !important
}

.text-amber-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(146 64 14 / var(--tw-text-opacity)) !important
}

.text-amber-900 {
  --tw-text-opacity: 1 !important;
  color: rgb(120 53 15 / var(--tw-text-opacity)) !important
}

.text-green-700 {
  --tw-text-opacity: 1 !important;
  color: rgb(21 128 61 / var(--tw-text-opacity)) !important
}

.text-green-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(22 101 52 / var(--tw-text-opacity)) !important
}

.text-green-900 {
  --tw-text-opacity: 1 !important;
  color: rgb(20 83 45 / var(--tw-text-opacity)) !important
}

.text-purple-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(147 51 234 / var(--tw-text-opacity)) !important
}

.text-purple-700 {
  --tw-text-opacity: 1 !important;
  color: rgb(126 34 206 / var(--tw-text-opacity)) !important
}

.text-purple-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(107 33 168 / var(--tw-text-opacity)) !important
}

.text-purple-900 {
  --tw-text-opacity: 1 !important;
  color: rgb(88 28 135 / var(--tw-text-opacity)) !important
}

.text-red-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(220 38 38 / var(--tw-text-opacity)) !important
}

.text-red-700 {
  --tw-text-opacity: 1 !important;
  color: rgb(185 28 28 / var(--tw-text-opacity)) !important
}

.text-red-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(153 27 27 / var(--tw-text-opacity)) !important
}

.text-red-900 {
  --tw-text-opacity: 1 !important;
  color: rgb(127 29 29 / var(--tw-text-opacity)) !important
}

.shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important
}

.hover\:w-48:hover {
  width: 12rem !important
}

@media (min-width: 0px) and (max-width: 600px) {
  .xs\:hidden {
    display: none !important
  }

  .xs\:w-14 {
    width: 3.5rem !important
  }

  .xs\:w-24 {
    width: 6rem !important
  }

  .xs\:w-40 {
    width: 10rem !important
  }

  .xs\:px-0 {
    padding-left: 0px !important;
    padding-right: 0px !important
  }
}

@media (min-width: 601px) and (max-width: 960px) {
  .sm\:visible {
    visibility: visible !important
  }

  .sm\:hidden {
    display: none !important
  }

  .sm\:h-7 {
    height: 1.75rem !important
  }

  .sm\:h-9 {
    height: 2.25rem !important
  }

  .sm\:h-\[62px\] {
    height: 62px !important
  }

  .sm\:w-14 {
    width: 3.5rem !important
  }

  .sm\:w-24 {
    width: 6rem !important
  }

  .sm\:w-40 {
    width: 10rem !important
  }

  .sm\:px-0 {
    padding-left: 0px !important;
    padding-right: 0px !important
  }
}

@media (min-width: 961px) and (max-width: 1280px) {
  .md\:mt-3 {
    margin-top: 0.75rem !important
  }

  .md\:block {
    display: block !important
  }

  .md\:hidden {
    display: none !important
  }

  .md\:h-7 {
    height: 1.75rem !important
  }

  .md\:h-9 {
    height: 2.25rem !important
  }

  .md\:h-\[62px\] {
    height: 62px !important
  }

  .md\:w-48 {
    width: 12rem !important
  }

  .md\:w-80 {
    width: 20rem !important
  }
}

@media (min-width: 1281px) and (max-width: 1920px) {
  .lg\:mt-3 {
    margin-top: 0.75rem !important
  }

  .lg\:block {
    display: block !important
  }

  .lg\:hidden {
    display: none !important
  }

  .lg\:h-7 {
    height: 1.75rem !important
  }

  .lg\:h-9 {
    height: 2.25rem !important
  }

  .lg\:h-\[62px\] {
    height: 62px !important
  }

  .lg\:w-48 {
    width: 12rem !important
  }

  .lg\:w-80 {
    width: 20rem !important
  }
}

@media (min-width: 1921px) and (max-width: 2560px) {
  .xl\:mt-2 {
    margin-top: 0.5rem !important
  }

  .xl\:block {
    display: block !important
  }

  .xl\:hidden {
    display: none !important
  }

  .xl\:h-7 {
    height: 1.75rem !important
  }

  .xl\:h-9 {
    height: 2.25rem !important
  }

  .xl\:h-\[62px\] {
    height: 62px !important
  }

  .xl\:w-48 {
    width: 12rem !important
  }

  .xl\:w-80 {
    width: 20rem !important
  }
}

@media (min-width: 2561px) {
  .xxl\:hidden {
    display: none !important
  }
}
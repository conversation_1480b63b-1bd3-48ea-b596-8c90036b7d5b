@using FluentBlue.UI.Main.Components
@using Microsoft.AspNetCore.Components.Routing
@using Icons = Microsoft.FluentUI.AspNetCore.Components.Icons

@rendermode @(new InteractiveWebAssemblyRenderMode(prerender: false))

<nav @attributes="@CustomAttributes">
    <FluentNavMenu Width="250" Collapsible="false" Title="menull" @bind-Expanded="@menuExpanded">
        <FluentNavLink Href="/" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size16.Home())" IconColor="Color.Accent">@Resources.NavMenuResource.Home</FluentNavLink>
        <FluentNavLink Href="contacts" Icon="@(new Icons.Regular.Size16.People())" IconColor="Color.Accent">@Resources.NavMenuResource.Contacts</FluentNavLink>
        <FluentNavLink Href="calendar" Icon="@(new Icons.Regular.Size16.Calendar())" IconColor="Color.Accent">@Resources.NavMenuResource.Calendar</FluentNavLink>
    </FluentNavMenu>
</nav>
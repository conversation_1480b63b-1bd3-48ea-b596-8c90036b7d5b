﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="15.0.1" />
    <PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Data\FluentBlue.Data.Model\FluentBlue.Data.Model.csproj" />
    <ProjectReference Include="..\..\Shared\FluentBlue.Shared\FluentBlue.Shared.csproj" />
    <ProjectReference Include="..\..\WebApi\FluentBlue.WebApi.Shared\FluentBlue.WebApi.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\ContactCategoriesResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ContactCategoriesResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\EventCategoriesResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EventCategoriesResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\EventStatesResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EventStatesResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\GlobalResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>GlobalResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\ContactCategoriesResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContactCategoriesResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\EventCategoriesResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\EventCategoriesResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EventCategoriesResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\EventStatesResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\EventStatesResource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>EventStatesResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\GlobalResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\GlobalResource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>GlobalResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>

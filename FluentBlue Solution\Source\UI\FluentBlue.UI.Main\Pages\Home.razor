﻿@page "/"
@layout MainLayout
@attribute [Authorize]

@rendermode @(new InteractiveWebAssemblyRenderMode(prerender: false))

@inject IDialogService dialogService
@inject ILogger<Home> logger
@inject HttpClient httpClient
@inject IConfiguration configuration
@inject NavigationManager navManager
@inject ILogger<FluentBlue.WebApi.Client.GeneralWebApiClient> generalWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger

<div id="pageTop" class="px-3 pt-3">
    @* <PageTitle>Home</PageTitle> *@

    <FluentGrid>
        <FluentGridItem xs="12" md="6" Gap="10">
            <FluentCard Height="100px">
                <FluentStack Orientation="Orientation.Horizontal" HorizontalGap="10" VerticalAlignment="VerticalAlignment.Center">
                    <FluentIcon Value="new Icons.Regular.Size48.People()" Color="Color.Custom" CustomColor="#0069A8" />
                    <div>
                        <FluentLabel Typo="Typography.H2">@this.summaryData!.ContactsCount @Resources.HomeResource.ContactsCount</FluentLabel>
                        <FluentLabel Typo="Typography.Body">@Resources.HomeResource.ContactsCountDescription</FluentLabel>
                    </div>
                </FluentStack>
            </FluentCard>
        </FluentGridItem>

        <FluentGridItem xs="12" md="6">
            <FluentCard Height="100px">
                <FluentStack Orientation="Orientation.Horizontal" HorizontalGap="10" VerticalAlignment="VerticalAlignment.Center">
                    <FluentIcon Value="new Icons.Regular.Size48.Person()" Color="Color.Custom" CustomColor="#E17100" />
                    <div>
                        <FluentLabel Typo="Typography.H2">@this.summaryData!.UsersCount @Resources.HomeResource.UsersCount</FluentLabel>
                        <FluentLabel Typo="Typography.Body">@Resources.HomeResource.UsersCountDescription</FluentLabel>
                    </div>
                </FluentStack>
            </FluentCard>
        </FluentGridItem>

        <!-- Second row -->
        <FluentGridItem xs="12" md="4" Gap="10">
            <FluentCard Height="100px" >
                <FluentStack Orientation="Orientation.Horizontal" HorizontalGap="10" VerticalAlignment="VerticalAlignment.Center">
                    <FluentIcon Value="new Icons.Regular.Size48.Calendar()" Color="Color.Custom" CustomColor="#009689" />
                    <div>
                        <FluentLabel Typo="Typography.H2">@this.summaryData!.TodayEventsCount @Resources.HomeResource.TodayEventsCount</FluentLabel>
                        <FluentLabel Typo="Typography.Body">@Resources.HomeResource.TodayEventsCountDescription</FluentLabel>
                    </div>
                </FluentStack>
            </FluentCard>
        </FluentGridItem>

        <FluentGridItem xs="12" md="4" Gap="10">
            <FluentCard Height="100px">
                <FluentStack Orientation="Orientation.Horizontal" HorizontalGap="10" VerticalAlignment="VerticalAlignment.Center">
                    <FluentIcon Value="new Icons.Regular.Size48.Calendar()" Color="Color.Custom" CustomColor="#009689" />
                    <div>
                        <FluentLabel Typo="Typography.H2">@this.summaryData!.TomorrowEventsCount @Resources.HomeResource.TomorrowEventsCount</FluentLabel>
                        <FluentLabel Typo="Typography.Body">@Resources.HomeResource.TomorrowEventsCountDescription</FluentLabel>
                    </div>
                </FluentStack>
            </FluentCard>
        </FluentGridItem>

        <FluentGridItem xs="12" md="4" Gap="10">
            <FluentCard Height="100px">
                <FluentStack Orientation="Orientation.Horizontal" HorizontalGap="10" VerticalAlignment="VerticalAlignment.Center">
                    <FluentIcon Value="new Icons.Regular.Size48.Calendar()" Color="Color.Custom" CustomColor="#009689" />
                    <div>
                        <FluentLabel Typo="Typography.H2">@this.summaryData!.FutureEventsCount @Resources.HomeResource.FutureEventsCount</FluentLabel>
                        <FluentLabel Typo="Typography.Body">@Resources.HomeResource.FutureEventsCountDescription</FluentLabel>
                    </div>
                </FluentStack>
            </FluentCard>
        </FluentGridItem>
     



@* 
        
        <FluentGridItem xs="12" md="3" Class="mt-4">
            <FluentCard Height="160px">
                <div class="flex flex-col h-full p-4">
                    <div class="flex items-center mb-2">
                        <FluentIcon Value="new Icons.Regular.Size48.ChartPerson()" Color="Color.Custom" CustomColor="#7008E7" />
                        <div>
                            <FluentLabel Typo="Typography.H3" Class="text-green-800">Revenue</FluentLabel>
                            <FluentLabel Typo="Typography.H2" Class="text-green-900 font-bold">$24,500</FluentLabel>
                        </div>
                    </div>
                    <FluentLabel Typo="Typography.Body" Class="text-green-700 mt-auto">Monthly revenue</FluentLabel>
                </div>
            </FluentCard>
        </FluentGridItem>

        <FluentGridItem xs="12" md="3" Class="mt-4">
            <FluentCard Height="160px">
                <div class="flex flex-col h-full p-4">
                    <div class="flex items-center mb-2">
                        <FluentIcon Value="new Icons.Regular.Size48.ShoppingBag()" Class="text-purple-600 mr-3" />
                        <div>
                            <FluentLabel Typo="Typography.H3" Class="text-purple-800">Orders</FluentLabel>
                            <FluentLabel Typo="Typography.H2" Class="text-purple-900 font-bold">450</FluentLabel>
                        </div>
                    </div>
                    <FluentLabel Typo="Typography.Body" Class="text-purple-700 mt-auto">New orders this month</FluentLabel>
                </div>
            </FluentCard>
        </FluentGridItem>

        <FluentGridItem xs="12" md="3" Class="mt-4">
            <FluentCard Height="160px" Class="shadow-none border bg-amber-100">
                <div class="flex flex-col h-full p-4">
                    <div class="flex items-center mb-2">
                        <FluentIcon Value="new Icons.Regular.Size48.Star()" Class="text-amber-600 mr-3" />
                        <div>
                            <FluentLabel Typo="Typography.H3" Class="text-amber-800">Reviews</FluentLabel>
                            <FluentLabel Typo="Typography.H2" Class="text-amber-900 font-bold">128</FluentLabel>
                        </div>
                    </div>
                    <FluentLabel Typo="Typography.Body" Class="text-amber-700 mt-auto">New customer reviews</FluentLabel>
                </div>
            </FluentCard>
        </FluentGridItem>

        <FluentGridItem xs="12" md="3" Class="mt-4">
            <FluentCard Height="160px" Class="shadow-none border bg-red-100">
                <div class="flex flex-col h-full p-4">
                    <div class="flex items-center mb-2">
                        <FluentIcon Value="new Icons.Regular.Size48.Alert()" Class="text-red-600 mr-3" />
                        <div>
                            <FluentLabel Typo="Typography.H3" Class="text-red-800">Issues</FluentLabel>
                            <FluentLabel Typo="Typography.H2" Class="text-red-900 font-bold">5</FluentLabel>
                        </div>
                    </div>
                    <FluentLabel Typo="Typography.Body" Class="text-red-700 mt-auto">Open support tickets</FluentLabel>
                </div>
            </FluentCard>
        </FluentGridItem> *@

    </FluentGrid>
    <br/>
    <FluentButton OnClick="buttonClicked"></FluentButton>
</div>

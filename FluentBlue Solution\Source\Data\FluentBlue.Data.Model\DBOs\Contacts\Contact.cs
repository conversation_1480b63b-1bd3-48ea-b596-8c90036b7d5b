﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.Extensions;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using Microsoft.Extensions.Options;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Net;
using System.Numerics;
using System.Runtime.CompilerServices;

namespace FluentBlue.Data.Model.DBOs.Contacts
{
    [Table("Contact", Schema = "Contacts")]
    public class Contact : IObjectState, INotifyPropertyChanged
    {
        private Guid contactId;
        private Guid? tenantId;
        private ContactType? type;
        private string firstName;
        private string middleName;
        private string lastName;
        private string occupation;
        private string tin;
        private string ssn;
        private decimal vat;
        private string notes;
        private string image;
        private string userTimeZoneId;
        public Contact()
        {
            contactId = Guid.CreateVersion7();  //TODO: Να αντικατασταθεί από το Guid.CreateVersion7()
            type = null;
            firstName = string.Empty;
            middleName = string.Empty;
            lastName = string.Empty;
            occupation = string.Empty;
            tin = string.Empty;
            ssn = string.Empty;
            vat = 0;
            notes = string.Empty;
            image = string.Empty;
            DateModifiedUtc = DateTime.UtcNow;
            RowVersion = new byte[0];
            ObjectState = ObjectState.Unchanged;
            this.userTimeZoneId = string.Empty;

            Emails = new List<ContactEmail>();
            Addresses = new List<ContactAddress>();
            Phones = new List<ContactPhone>();
            ContactCategoryMappings = new List<ContactCategoryMapping>();
        }

        [Key]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.ContactId))]
        public Guid ContactId
        {
            get
            {
                return contactId;
            }
            set
            {
                contactId = value;
                NotifyPropertyChanged();
            }
        }   //TODO: Στη βάση δεδομένων πρέπει το primary key να γίνει από non-clustered index σε clustered για λόγους ταχύτητας (τα GUID ειναι αργά με Clustered index).

        //[Required]
        public Guid? TenantId
        {
            get
            {
                return tenantId;
            }
            set
            {
                tenantId = value;
                NotifyPropertyChanged();
            }
        }

        [ForeignKey("TenantId")]
        public Tenant? Tenant { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.Type))]
        //[Required(ErrorMessageResourceType = typeof(FluentBlue.Data.Model.Resources.Contacts.ContactResource), ErrorMessageResourceName = nameof(FluentBlue.Data.Model.Resources.Contacts.ContactResource.FieldRequired))]
        public ContactType? Type
        {
            get
            {
                return type;
            }
            set
            {
                type = value.HasValue ? value : null;
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.FirstName))]
        //[MaxLength(100, ErrorMessageResourceType = typeof(Model.Resources.Contacts.ContactResource), ErrorMessageResourceName = nameof(Model.Resources.Contacts.ContactResource.FirstNameMaxLengthError))]
        //[Required(ErrorMessageResourceName = nameof(Model.Resources.GeneralValidationResource.FieldRequired), ErrorMessageResourceType = typeof(Model.Resources.GeneralValidationResource))]
        public string FirstName
        {
            get
            {
                return firstName;
            }
            set
            {
                firstName = value ?? string.Empty;
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.LastName))]
        //[MaxLength(100, ErrorMessageResourceType = typeof(Model.Resources.Contacts.ContactResource), ErrorMessageResourceName = nameof(Model.Resources.Contacts.ContactResource.LastNameMaxLengthError))]
        //[Required(ErrorMessageResourceName = nameof(Model.Resources.GeneralValidationResource.FieldRequired), ErrorMessageResourceType = typeof(Model.Resources.GeneralValidationResource))]
        public string LastName
        {
            get
            {
                return lastName;
            }
            set
            {
                lastName = value ?? string.Empty;
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.MiddleName))]
        //[MaxLength(100)]
        //[DefaultValue("")]
        public string MiddleName
        {
            get
            {
                return middleName;
            }
            set
            {
                middleName = value ?? "";
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.Occupation))]
        //[MaxLength(200)]
        [DefaultValue("")]
        public string Occupation
        {
            get
            {
                return occupation;
            }
            set
            {
                occupation = value ?? "";
                NotifyPropertyChanged();
            }
        }

        /// <summary>
        /// Tax Identification Number
        /// </summary>
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.TIN))]
        //[MaxLength(100)]
        [DefaultValue("")]
        public string TIN
        {
            get
            {
                return tin;
            }
            set
            {
                tin = value ?? "";
                NotifyPropertyChanged();
            }
        }

        /// <summary>
        /// Social Security Number
        /// </summary>
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.SSN))]
        //[MaxLength(100)]
        [DefaultValue("")]
        public string SSN
        {
            get
            {
                return ssn;
            }
            set
            {
                ssn = value;
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.Vat))]
        [Column(TypeName = "decimal(6,2)")]
        [DefaultValue(0)]
        public decimal Vat
        {
            get
            {
                return vat;
            }
            set
            {
                vat = value;
                NotifyPropertyChanged();
            }
        }

        /// <summary>
        /// Notes
        /// </summary>
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.Notes))]
        //[MaxLength(5000)]
        [DefaultValue("")]
        public string Notes
        {
            get
            {
                return notes;
            }
            set
            {
                notes = value ?? "";
                NotifyPropertyChanged();
            }
        }


        [Display(Name = "Εμαιλς")]
        public List<ContactEmail> Emails { get; set; }

        public List<ContactAddress> Addresses { get; set; }

        public List<ContactPhone> Phones { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.ContactCategories))]
        public List<ContactCategoryMapping> ContactCategoryMappings { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.Image))]
        [Column(TypeName = "nvarchar(max)")]
        public string Image
        {
            get
            {
                return image;
            }
            set
            {
                image = value;
                NotifyPropertyChanged();
            }
        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.FullName))]
        public string FullName
        {
            get
            {
                return FirstName + " " + LastName;
            }
        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.Summary))]
        public string Summary
        {
            get
            {
                return FirstName + " " + LastName;
            }
        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.PhoneNumbers))]
        public string PhoneNumbers
        {
            get
            {
                return string.Join(", ", Phones.OrderBy(x => x.Type).Select(x=>x.PhoneNumber).ToList());
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        [Column(TypeName = "datetime")]
        public DateTime DateCreatedUtc { get; set; }

        [NotMapped]
        public DateTime? DateCreatedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateCreatedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateCreatedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        [Column(TypeName = "datetime")]
        public DateTime DateModifiedUtc { get; set; }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        [NotMapped]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
                NotifyPropertyChanged();
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }

        [NotMapped]
        public string Initials
        {
            get
            {
                return (FirstName.Substring(0, 1) ?? "") + (LastName.Substring(0, 1) ?? "");
            }
            //set { }
        }

        [NotMapped]
        public string UserTimeZoneId
        {
            get
            {
                return this.userTimeZoneId;
            }
            set
            {
                this.userTimeZoneId = value;
                foreach (var phone in Phones)
                {
                    phone.UserTimeZoneId = value;
                }
                foreach (var email in Emails)
                {
                    email.UserTimeZoneId = value;
                }
                foreach (var address in Addresses)
                {
                    address.UserTimeZoneId = value;
                }
            }
        }

        [NotMapped]
        public bool NotifyChildrenPropertyChangedEnabled
        {
            get
            {
                return false;  //Χρειάζεται γιατί αλλιώς βγάζει σφάλμα στο Syncfusion Scheduler όταν πάει να κάνει clone το αντικείμενο Contact.
            }
            set
            {
                foreach (var phone in Phones)
                {
                    phone.NotifyPropertyChangedEnabled = value;
                }
                foreach (var email in Emails)
                {
                    email.NotifyPropertyChangedEnabled = value;
                }
                foreach (var address in Addresses)
                {
                    address.NotifyPropertyChangedEnabled = value;
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            //DateModifiedUtc = DateTime.UtcNow;

            //Έγινε σχόλιο γιατί τρέχει πολύ συχνά και γίνεται Modified χωρίς λόγο.
            //if (ObjectState == ObjectState.Unchanged)
            //{
            //    ObjectState = ObjectState.Modified;
            //}

            if (PropertyChanged != null)
            {
                PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
            }

        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.FullName))]
        public string FirstLastName
        {
            get
            {
                return FirstName + " " + LastName;
            }
            //set { }
        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.FullName))]
        public string LastFirstName
        {
            get
            {
                return LastName + " " + FirstName;
            }
            //set { }
        }

        #region  Data Manipulation Methods
        public ContactPhone NewContactPhone()
        {
            ContactPhone phone = ContactPhone.NewContactPhone();
            phone.ContactId = ContactId;
            phone.UserTimeZoneId = this.userTimeZoneId;
            phone.ObjectState = ObjectState.Added;
            Phones.Add(phone);

            return phone;
        }

        public ContactAddress NewContactAddress()
        {
            ContactAddress address = ContactAddress.NewContactAddress();
            address.ContactId = ContactId;
            address.UserTimeZoneId = this.userTimeZoneId;
            address.ObjectState = ObjectState.Added;
            Addresses.Add(address);

            return address;
        }

        public ContactEmail NewContactEmail()
        {
            ContactEmail email = new ContactEmail();
            email.ContactId = ContactId;
            email.UserTimeZoneId = this.userTimeZoneId;
            email.ObjectState = ObjectState.Added;
            Emails.Add(email);

            return email;
        }

        public ContactCategoryMapping NewContactCategoryMapping()
        {
            ContactCategoryMapping contactCategoryMapping = new ContactCategoryMapping();
            contactCategoryMapping.ContactId = ContactId;
            contactCategoryMapping.ObjectState = ObjectState.Added;
            ContactCategoryMappings.Add(contactCategoryMapping);

            return contactCategoryMapping;
        }
        #endregion


        public static Contact CreateContact(Guid tenantId)
        {
            Contact contact = new Contact();
            contact.TenantId = tenantId;
            contact.ObjectState = ObjectState.Added;

            return contact;
        }
    }
}

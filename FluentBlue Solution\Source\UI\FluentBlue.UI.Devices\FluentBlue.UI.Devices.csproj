﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android;net9.0-ios</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net8.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
            The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
            When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
            The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
            either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->
		
		<OutputType>Exe</OutputType>
		<RootNamespace>FluentBlue.UI.Devices</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<EnableDefaultCssItems>false</EnableDefaultCssItems>
		<Nullable>enable</Nullable>



		<!-- Display name -->
		<ApplicationTitle>FluentBlue.UI.Devices</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.intelsoft.fluentblue</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">14.2</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">14.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">24.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.20348.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.20348.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-android|AnyCPU'">
		<RunAOTCompilation>False</RunAOTCompilation>
		<PublishTrimmed>false</PublishTrimmed>
	</PropertyGroup>

	<PropertyGroup>
		<BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
	</PropertyGroup>

	<PropertyGroup>
	  <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.26100.0</TargetFrameworks>
	  <GenerateAppInstallerFile>False</GenerateAppInstallerFile>
	  <AppxPackageSigningEnabled>False</AppxPackageSigningEnabled>
	  <AppxPackageSigningTimestampDigestAlgorithm>SHA256</AppxPackageSigningTimestampDigestAlgorithm>
	  <AppxAutoIncrementPackageRevision>True</AppxAutoIncrementPackageRevision>
	  <AppxSymbolPackageEnabled>False</AppxSymbolPackageEnabled>
	  <GenerateTestArtifacts>True</GenerateTestArtifacts>
	  <HoursBetweenUpdateChecks>0</HoursBetweenUpdateChecks>
	</PropertyGroup>
	
	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.svg" BaseSize="168,208" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
		<AndroidResource Remove="Components\Layout\**" />
		<AndroidResource Remove="Components\Pages\**" />
		<Compile Remove="Components\Layout\**" />
		<Compile Remove="Components\Pages\**" />
		<Content Remove="Components\Layout\**" />
		<Content Remove="Components\Pages\**" />
		<EmbeddedResource Remove="Components\Layout\**" />
		<EmbeddedResource Remove="Components\Pages\**" />
		<MauiXaml Remove="Components\Layout\**" />
		<MauiXaml Remove="Components\Pages\**" />
		<None Remove="Components\Layout\**" />
		<None Remove="Components\Pages\**" />
	</ItemGroup>

	<ItemGroup>
		<Content Remove="appsettings.Development.json" />
		<Content Remove="appsettings.Production.json" />
		<Content Remove="Platforms\Android\google-services.json" />
		<Content Remove="wwwroot\appsettings.json" />
		<Content Remove="wwwroot\css\app ORIGINAL.css" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="appsettings.Development.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
		<EmbeddedResource Include="appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
		<EmbeddedResource Include="appsettings.Production.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.12.1" />
		<PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.12.1" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="9.0.100" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.8" />
		<PackageReference Include="Sentry.Maui" Version="5.14.1" />
		<PackageReference Include="Sentry.Serilog" Version="5.14.1" />
		<PackageReference Include="Serilog" Version="4.3.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\FluentBlue.UI.Main\FluentBlue.UI.Main.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Components\" />
		<Folder Include="wwwroot\css\" />
	</ItemGroup>

	<ItemGroup>
	  <GoogleServicesJson Include="Platforms\Android\google-services.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.100" />
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.100" />
	</ItemGroup>

	<ItemGroup Condition="'$(TargetFramework)' == 'net9.0-android'">
	  <PackageReference Include="Xamarin.AndroidX.Lifecycle.Common">
	    <Version>2.9.2.1</Version>
	  </PackageReference>
	  <PackageReference Include="Xamarin.AndroidX.Fragment" Version="1.8.8.1" />
	  <PackageReference Include="Xamarin.AndroidX.Lifecycle.LiveData.Core" Version="2.9.2.1" />
      <PackageReference Include="Xamarin.AndroidX.Lifecycle.LiveData" Version="2.9.2.1" />
	  <PackageVersion Include="Xamarin.AndroidX.Lifecycle.ViewModel" Version="2.9.2.1" />
	  <PackageReference Include="Xamarin.AndroidX.SavedState.SavedState.Ktx">
	    <Version>1.3.1.1</Version>
		<ExcludeAssets>true</ExcludeAssets>
	  </PackageReference>
	  <PackageReference Include="Xamarin.Firebase.Messaging">
	    <Version>124.0.3.1</Version>
	  </PackageReference>
	  <PackageReference Include="Xamarin.Google.Dagger">
	    <Version>2.56.2.2</Version>
	  </PackageReference>
	  <PackageReference Include="Xamarin.GooglePlayServices.Base">
	    <Version>118.7.2.1</Version>
	  </PackageReference>
	</ItemGroup>
</Project>

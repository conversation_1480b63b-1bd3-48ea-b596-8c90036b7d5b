﻿@page "/calendar"

@using FluentBlue.Data.Model.DBOs.Calendar
@using FluentBlue.Data.Model.DTOs
@using FluentBlue.Shared.Utilities
@using FluentBlue.UI.Main.Services
@using Microsoft.Extensions.Caching.Hybrid
@using Microsoft.JSInterop
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Schedule

@layout MainLayout
@attribute [Authorize]

@inject HttpClient httpClient
@inject IConfiguration configuration
@inject NavigationManager navManager
@inject IDialogService dialogService
@inject IToastService toastService
@inject ILogger<Calendar> logger
@inject ILogger<FluentBlue.WebApi.Client.EventsWebApiClient> eventsWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.EventCategoriesWebApiClient> eventCategoriesWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.EventStatesWebApiClient> eventStatesWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.SettingsWebApiClient> settingsWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.ContactsWebApiClient> contactsWebApiClientLogger
@inject HybridCache cache
@inject IFormFactor formFactor
@inject SignalRNotificationService notificationService
@inject IJSRuntime jsRuntime

@rendermode @(new InteractiveWebAssemblyRenderMode(prerender: false))


@if (formFactor.GetPlatform() == "Web")
{
    @* <InteractiveWebAssemblyRenderMode></InteractiveWebAssemblyRenderMode> *@
    @* @rendermode @(new InteractiveWebAssemblyRenderMode(false)) *@
}

<div id="pageTop" class="px-3 pt-3">
    @* <FluentLabel Typo="Typography.H1" Class="mb-2">@Resources.CalendarResource.Title</FluentLabel> *@

    <FluentToolbar Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
        <FluentButton IconStart="@(new Icons.Regular.Size20.Calendar())" Appearance="Appearance.Accent" OnClick="@NewEvent"><span>@Resources.CalendarResource.NewEventBtn_Text</span></FluentButton>
        <FluentButton IconStart="@(new Icons.Regular.Size20.ArrowSync())" Appearance="Appearance.Outline" OnClick="@Sync"><span class="xs:hidden sm:hidden">@GlobalResource.Sync</span></FluentButton>
        <div style="width:160px;">
            <FluentAutocomplete @ref="@displayUsersDropDown" Items="@displayUsersDS" @bind-SelectedOptions="@this.selectedDisplayUsers" TOption="UserLI" IconSearch="null" KeepOpen="false" Appearance="FluentInputAppearance.Filled" MaximumSelectedOptions="4" OptionValue="@(x => x.UserId.ToString())" OptionText="@(x => x.FullName)" IconDismiss="null" AutoComplete="off" ValueChanged="@DisplayUsersDropDownOnValueChanged">
                @* Template used with each Selected items *@
                <SelectedOptionTemplate>
                    <FluentPersona ImageSize="24px" Initials="@context.Initials" Style=@("background-color: " + FluentBlue.Shared.Utilities.Colors.GetColorFromInitials(context.Initials)) DismissTitle="" />
                </SelectedOptionTemplate>

                @* Template used with each Option items *@
                <OptionTemplate>
                    <FluentStack Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal">
                        <FluentPersona ImageSize="24px" Initials="@context.Initials" Style=@("background-color: " + FluentBlue.Shared.Utilities.Colors.GetColorFromInitials(context.Initials)) />
                        <FluentLabel>@context.FullName</FluentLabel>
                    </FluentStack>
                </OptionTemplate>

                @* Template used when the maximum number of selected items (MaximumSelectedOptions) has been reached *@
                <MaximumSelectedOptionsMessage>
                    @Resources.CalendarResource.MaximumCalendarVisisbleUsersSelected
                </MaximumSelectedOptionsMessage>

                @* Content display at the top of the Popup area *@
                @*  <HeaderContent>
                <FluentLabel Color="Color.Accent"
                             Style="padding: 8px; font-size: 11px; border-bottom: 1px solid var(--neutral-fill-stealth-hover);">
                    Suggested contacts
                </FluentLabel>
            </HeaderContent> *@

                @* Content display at the bottom of the Popup area *@
                <FooterContent>
                    @if (!context.Any())
                    {
                        <FluentLabel Style="font-size: 11px; text-align: center; width: 200px;">
                            @Resources.CalendarResource.NoUsersFound
                        </FluentLabel>
                    }
                </FooterContent>
            </FluentAutocomplete>
        </div>
        <FluentButton id="printCalendarBtn" IconStart="@(new Icons.Regular.Size20.Print())" Appearance="Appearance.Neutral" OnClick="@PrintCalendar" Class="xs:hidden sm:hidden"></FluentButton>
        <FluentTooltip Anchor="printCalendarBtn">
            @Resources.CalendarResource.PrintCalendarTooltip
        </FluentTooltip>
        <FluentButton Id="exportCalendarToExcelBtn" IconStart="@(new Icons.Regular.Size20.ArrowExport())" Appearance="Appearance.Neutral" OnClick="@ExportCalendarToExcel" Class="xs:hidden sm:hidden"></FluentButton>
        <FluentTooltip Anchor="exportCalendarToExcelBtn">
            @Resources.CalendarResource.ExportToExcel
        </FluentTooltip>
        @* <FluentSearch slot="end" @ref="searchTxtBox" @onkeyup="Search_OnKeyUp" ValueChanged="Search_ValueChanged"></FluentSearch> *@
        <FluentButton Id="actionsBtn" slot="end" IconStart="@(new Icons.Regular.Size16.MoreHorizontal())" OnClick="@(() => { actionsBtnOpened = !actionsBtnOpened; StateHasChangedOptimized(); })" Appearance="Appearance.Outline" Class="md:hidden lg:hidden xl:hidden xxl:hidden"></FluentButton>
        <FluentMenu Anchor="actionsBtn" @bind-Open="actionsBtnOpened" Anchored="true" UseMenuService="true">
            <FluentMenuItem OnClick="@PrintCalendar"><span slot="start"><FluentIcon Value="@(new Icons.Regular.Size20.Print())"></FluentIcon></span>@GlobalResource.Print</FluentMenuItem>
            <FluentMenuItem OnClick="@ExportCalendarToExcel"><span slot="start"><FluentIcon Value="@(new Icons.Regular.Size20.ArrowExport())"></FluentIcon></span>@Resources.CalendarResource.ExportToExcel</FluentMenuItem>
        </FluentMenu>
    </FluentToolbar>
</div>

@if (this.events != null)
{
    <SfSchedule ID="schedule" @ref="@schedule" TValue="Data.Model.DBOs.Calendar.Event" Height="calc(100vh - 127px)" AllowMultiDrag="false" EnableAdaptiveUI="@enableAdaptiveUI" AgendaDaysCount="this.agendaDaysCount" @bind-SelectedDate="@this.CurrentDate" Timezone="@this.timeZone" CssClass="border-x-0 border-b-0">
        <ScheduleWorkHours Highlight="true" Start="@this.workStartHour" End="@this.workEndHour"></ScheduleWorkHours>
        <ScheduleTimeScale Interval="@this.interval" SlotCount="@this.slotsCount"></ScheduleTimeScale>
        <ScheduleEventSettings DataSource="@events" EnableTooltip="true" ResourceColorField="@resourceColor">
            <ScheduleField Id="EventId">
                <FieldSubject Name="Summary"></FieldSubject>
                <FieldDescription Name="PlainDescription"></FieldDescription>
                <FieldStartTime Name="StartTimeLocal"></FieldStartTime>
                <FieldEndTime Name="EndTimeLocal"></FieldEndTime>
                <FieldIsAllDay Name="AllDay"></FieldIsAllDay>
                <FieldStartTimezone Name="TimeZoneLocal"></FieldStartTimezone>
                <FieldEndTimezone Name="TimeZoneLocal"></FieldEndTimezone>
            </ScheduleField>
        </ScheduleEventSettings>
        <ScheduleTemplates>

            <ResourceHeaderTemplate>
                @{
                    var resourceData = (context as TemplateContext).ResourceData as UserLI;
                    <div class="flex flex-wrap">
                        <div class="flex-none w-10">
                            <FluentPersona ImageSize="24px" Initials="@resourceData!.Initials" Style=@("height: 24px; background-color: " + FluentBlue.Shared.Utilities.Colors.GetColorFromInitials(resourceData!.Initials)) DismissTitle="" />
                        </div>
                        <div class="flex-auto inline-block text-left">
                            <span class="align-middle">@(resourceData.FullName)</span>
                        </div>
                    </div>
                }
            </ResourceHeaderTemplate>
        </ScheduleTemplates>
        <ScheduleGroup AllowGroupEdit="true" Resources="@resourcesGroup"></ScheduleGroup>

        <ScheduleResources>
            <ScheduleResource TItem="UserLI" TValue="Guid[]" DataSource="@this.displayedUsersInScheduler" Field="EventUserIds" Title="User" Name="Users"
                              TextField="FullName" IdField="UserId" AllowMultiple="true"></ScheduleResource>
        </ScheduleResources>
        <ScheduleEvents TValue="Data.Model.DBOs.Calendar.Event" OnCellDoubleClick="ScheduleOnCellDoubleClick" DataBinding="ScheduleOnDataBinding" DataBound="ScheduleOnDataBound" OnDragStart="ScheduleOnDragStart" Resized="ScheduleOnResized" EventRendered="ScheduleOnEventRendered" Dragged="ScheduleOnDragged" OnEventDoubleClick="ScheduleOnEventDoubleClick" OnEventClick="ScheduleOnEventClick" OnActionBegin="ScheduleOnActionBegin" OnPopupOpen="ScheduleOnPopupOpen" OnPopupClose="ScheduleOnPopupClose" ActionCompleted="ScheduleOnActionCompleted"></ScheduleEvents>
        <ScheduleViews>
            <ScheduleView Option="View.Day">
                <EventTemplate>
                    @{
                        var eventData = (context as Event);
                        <div>
                            <div class="e-subject">@eventData.Summary</div>
                            <div class="e-time">@(eventData.StartTimeLocal.Value.ToShortTimeString() + "-" + @eventData.EndTimeLocal.Value.ToShortTimeString())</div>
                        </div>
                    }
                </EventTemplate>
            </ScheduleView>
            <ScheduleView Option="View.Week">
                <EventTemplate>
                    @{
                        var eventData = (context as Event);
                        <div>
                            <div class="e-subject">@eventData.Summary</div>
                            <div class="e-time">@(eventData.StartTimeLocal.Value.ToShortTimeString() + "-" + @eventData.EndTimeLocal.Value.ToShortTimeString())</div>
                        </div>
                    }
                </EventTemplate>
            </ScheduleView>
            <ScheduleView Option="View.WorkWeek">
                <EventTemplate>
                    @{
                        var eventData = (context as Event);
                        <div>
                            <div class="e-subject">@eventData.Summary</div>
                            <div class="e-time">@(eventData.StartTimeLocal.Value.ToShortTimeString() + "-" + @eventData.EndTimeLocal.Value.ToShortTimeString())</div>
                        </div>
                    }
                </EventTemplate>
            </ScheduleView>
            <ScheduleView Option="View.Month" MaxEventsPerRow="4">
                <EventTemplate>
                    @{
                        var eventData = (context as Event);
                        <div>
                            <div class="e-subject">@eventData.Summary</div>
                            <div class="e-time">@(eventData.StartTimeLocal.Value.ToShortTimeString() + "-" + @eventData.EndTimeLocal.Value.ToShortTimeString())</div>
                        </div>
                    }
                </EventTemplate>
            </ScheduleView>
            <ScheduleView Option="View.Agenda" AllowVirtualScrolling="true"></ScheduleView>
        </ScheduleViews>
        @*  <ScheduleQuickInfoTemplates>
    <ContentTemplate>

    <div class="event-content">
    <div class="meeting-type-wrap">
    <label>Subject</label>:
    <span>@((context as Data.Model.DBOs.Calendar.Event).Subject)</span>
    </div>

    <div class="notes-wrap">
    <label>Notes</label>:
    <span>@((context as Data.Model.DBOs.Calendar.Event).Description)</span>
    </div>
    </div>


    <div class="e-date-time">
    <div class="e-date-time-icon e-icons"></div>
    <div class="e-date-time-details e-text-ellipsis"><span>@((context as Data.Model.DBOs.Calendar.Event).StartTime)</span></div>
    </div>
    </ContentTemplate>

    </ScheduleQuickInfoTemplates>  *@
    </SfSchedule>
}

<SfContextMenu @ref="@calendarContextMenu" TValue="MenuItem" Target=".e-schedule">
    <MenuItems>
        <MenuItem Text="@Resources.CalendarResource.NewEvent" Id="NewEvent" IconCss="e-icons e-plus" Hidden="@(!menuClickedCell || contextMenuCell == null)">
        </MenuItem>
        <MenuItem Text="@Resources.CalendarResource.EditEvent" Id="EditEvent" IconCss="e-icons e-edit" Hidden="@(!menuClickedEvent)">
        </MenuItem>
        @* <MenuItem Text="@Resources.CalendarResource.EditEvent" Id="EditRecurrenceEvent" IconCss="e-icons e-edit" Hidden="@(!menuClickedRecurrentEvent)">
            <MenuItems>
                <MenuItem Text="Edit Occurrence" Id="EditOccurrence" IconCss="e-icons e-occurrence"></MenuItem>
                <MenuItem Text="Edit Series" Id="EditSeries" IconCss="e-icons e-series"></MenuItem>
            </MenuItems>
        </MenuItem> *@
        <MenuItem Text="@Resources.CalendarResource.DeleteEvent" Id="Delete" IconCss="e-icons e-trash" Hidden="@(!menuClickedEvent)">
        </MenuItem>
        @*<MenuItem Text="@Resources.CalendarResource.DeleteEvent" Id="DeleteRecurrenceEvent" IconCss="e-icons e-trash" Hidden="@(!menuClickedRecurrentEvent)">
            <MenuItems>
                <MenuItem Text="Delete Occurrence" Id="DeleteOccurrence" IconCss="e-icons e-occurrence"></MenuItem>
                <MenuItem Text="Delete Series" Id="DeleteSeries" IconCss="e-icons e-series"></MenuItem>
            </MenuItems>
        </MenuItem>*@
        <MenuItem Separator="true"></MenuItem>
        <MenuItem Id="CopyEvent" Text="@Resources.CalendarResource.CopyEvent" IconCss="e-icons e-copy" Disabled="@(!menuClickedEvent)"></MenuItem>
        <MenuItem Id="CutEvent" Text="@Resources.CalendarResource.CutEvent" IconCss="e-icons e-cut" Disabled="@(!menuClickedEvent)"></MenuItem>
        <MenuItem Id="PasteEvent" Text="@Resources.CalendarResource.PasteEvent" IconCss="e-icons e-paste"></MenuItem>
        <MenuItem Id="DuplicateEvent" Text="@Resources.CalendarResource.DuplicateEvent" IconCss="e-icons e-duplicate" Hidden="@(!menuClickedEvent)"></MenuItem>
    </MenuItems>
    <MenuEvents TValue="MenuItem" OnOpen="ScheduleMenuOnOpen" ItemSelected="ScheduleMenuOnItemSelected" OnClose="ScheduleMenuOnClose"></MenuEvents>
</SfContextMenu>

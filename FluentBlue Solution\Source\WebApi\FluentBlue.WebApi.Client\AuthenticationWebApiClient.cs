﻿using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared.Authorization;
using FluentBlue.WebApi.Shared.Response;
using FluentBlue.WebApi.Shared.Request;
using Newtonsoft.Json;
using System.Text;

namespace FluentBlue.WebApi.Client
{
    public class AuthenticationWebApiClient
    {
        private HttpClient httpClient;
        //private readonly IHttpService httpService;

        public AuthenticationWebApiClient(HttpClient httpClient)
        {
            this.httpClient = httpClient;
        }

        public async Task<LoginResponse> Login(string username, string password)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + @"Authentication/Login";

                requestUri += "?Username=" + username + "&Password=" + password;

                string responseString = await this.httpClient.GetStringAsync(requestUri);
                ApiResponse<LoginResponse>? response = JsonConvert.DeserializeObject<ApiResponse<LoginResponse>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                //this.logger.LogError(ex, "Error in GetContactsLI({@tenantId})", tenantId);
                throw;
            }
        }

        public async Task<UserToken> RenewToken()
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + "RenewToken";

                string responseString = await this.httpClient.GetStringAsync(requestUri);
                ApiResponse<UserToken>? response = JsonConvert.DeserializeObject<ApiResponse<UserToken>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                //this.logger.LogError(ex, "Error in GetContactsLI({@tenantId})", tenantId);
                throw;
            }
        }

        public async Task<ForgotPasswordResponse> ForgotPasswordAsync(string email)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + "Authentication/ForgotPassword";

                var request = new ForgotPasswordRequest { Email = email };
                string requestJson = System.Text.Json.JsonSerializer.Serialize(request);
                HttpContent httpContent = new StringContent(requestJson, Encoding.UTF8, "application/json");

                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContent);
                string responseString = await httpResponse.Content.ReadAsStringAsync();
                ApiResponse<ForgotPasswordResponse>? response = JsonConvert.DeserializeObject<ApiResponse<ForgotPasswordResponse>>(responseString);

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return new ForgotPasswordResponse { Success = false, Message = "Unknown error occurred." };
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<ResetPasswordResponse> ResetPasswordAsync(string token, string newPassword)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + "Authentication/ResetPassword";

                var request = new ResetPasswordRequest { Token = token, NewPassword = newPassword };
                string requestJson = System.Text.Json.JsonSerializer.Serialize(request);
                HttpContent httpContent = new StringContent(requestJson, Encoding.UTF8, "application/json");

                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContent);
                string responseString = await httpResponse.Content.ReadAsStringAsync();
                ApiResponse<ResetPasswordResponse>? response = JsonConvert.DeserializeObject<ApiResponse<ResetPasswordResponse>>(responseString);

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return new ResetPasswordResponse { Success = false, Message = "Unknown error occurred." };
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<User> RegisterUserAsync(RegisterUserRequest request)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + "Authentication/Register";

                string requestJson = System.Text.Json.JsonSerializer.Serialize(request);
                HttpContent httpContent = new StringContent(requestJson, Encoding.UTF8, "application/json");

                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContent);
                string responseString = await httpResponse.Content.ReadAsStringAsync();
                ApiResponse<User>? response = JsonConvert.DeserializeObject<ApiResponse<User>>(responseString);

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                throw new Exception("Unknown error occurred during registration.");
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<TokenValidationResponse> ValidateResetTokenAsync(string token)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + $"Authentication/ValidateResetToken?token={Uri.EscapeDataString(token)}";

                string responseString = await this.httpClient.GetStringAsync(requestUri);
                ApiResponse<TokenValidationResponse>? response = JsonConvert.DeserializeObject<ApiResponse<TokenValidationResponse>>(responseString);

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return new TokenValidationResponse { IsValid = false, Message = "Unknown error occurred." };
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}

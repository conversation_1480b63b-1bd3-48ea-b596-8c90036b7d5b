﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Blazored.FluentValidation;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using FluentValidation;
using System.ComponentModel;
using System.Linq.Expressions;
using FluentBlue.Data.Model.DTOs;
using Microsoft.JSInterop;
using FluentBlue.Shared.Utilities;

namespace FluentBlue.UI.Main.Components
{
    public class EventCategoryColor
    {
        public string Color { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public partial class EventCategoriesDialog
    {
        [Parameter] public List<EventCategory>? Content { get; set; } = new List<EventCategory>();
        [CascadingParameter] public FluentDialog CurrentDialog { get; set; } = default!;
        private FluentValidationValidator? fluentValidationValidator = new FluentValidationValidator();
        private UserSetting? userSetting;
        private EventCategoryValidator eventCategoryValidator = new EventCategoryValidator();
        private List<EventCategoryColor> predefinedColors = new List<EventCategoryColor>
        {
            new EventCategoryColor { Color = "#cd5c5c", Name = "Indian Red" },
            new EventCategoryColor { Color = "#AC4F5E", Name = "Dark Red" },
            new EventCategoryColor { Color = "#DC626D", Name = "Berry" },
            //new EventCategoryColor { Color = "#E9835E", Name = "DarkOrange" },
            new EventCategoryColor { Color = "#CA8057", Name = "Bronze" },
            new EventCategoryColor { Color = "#FFBA66", Name = "Peach" },
            new EventCategoryColor { Color = "#f0e68c", Name = "Orange" },
            new EventCategoryColor { Color = "#DAC157", Name = "Gold" },
            new EventCategoryColor { Color = "#946B5C", Name = "Brown" },
            new EventCategoryColor { Color = "#9acd32", Name = "Yellow Green" },
            new EventCategoryColor { Color = "#85B44C", Name = "Forest" },
            //new EventCategoryColor { Color = "#5EC75A", Name = "Green" },
            new EventCategoryColor { Color = "#4DA64D", Name = "Dark Green" },
            new EventCategoryColor { Color = "#58D3DB", Name = "Petrol" },
            new EventCategoryColor { Color = "#41A3A3", Name = "Dark Petrol" },
            new EventCategoryColor { Color = "#4682b4", Name = "SteelBlue" },
            //new EventCategoryColor { Color = "#4178A3", Name = "Blue" },
            new EventCategoryColor { Color = "#A79CF1", Name = "Levanda" },
            new EventCategoryColor { Color = "#7E5CA7", Name = "Purple" },
            new EventCategoryColor { Color = "#EF85C8", Name = "Pink" },
            new EventCategoryColor { Color = "#AD4589", Name = "Damask" },
            //new EventCategoryColor { Color = "#AFABAA", Name = "Beige" },
            new EventCategoryColor { Color = "#B3BFC2", Name = "Silver" },
            //new EventCategoryColor { Color = "#888888", Name = "anthracite" },
            new EventCategoryColor { Color = "#808080", Name = "Gray" }
        };
        private bool isSaving = false;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                if (fluentValidationValidator != null)
                {
                    fluentValidationValidator.Validator = eventCategoryValidator;
                }

                await base.OnInitializedAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private void OnEventCategoryPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            try
            {
                // Handle property change
                if (sender is EventCategory category)
                {
                    if (category.ObjectState == ObjectState.Unchanged)
                    {
                        category.ObjectState = ObjectState.Modified;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    // Reads the UserSettings from cache or database
                    var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);

                    // Reads the EventCategories from cache or database
                    var eventCategoriesCacheOptions = new HybridCacheEntryOptions{LocalCacheExpiration = TimeSpan.FromMinutes(60)};

                    Content = await cache.GetOrCreateAsync(Keywords.EventCategories,
                        async cancel => {
                            EventCategoriesWebApiClient eventCategoriesWebApiClient = new EventCategoriesWebApiClient(httpClient, eventCategoriesWebApiClientLogger);
                            return await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);
                        }, eventCategoriesCacheOptions);

                    // Set timezone for each category
                    foreach (var category in Content!)
                    {
                        category.UserTimeZoneId = this.userSetting!.TimeZone;
                        category.NotifyPropertyChangedEnabled = true;
                        //category.PropertyChanged += OnEventCategoryPropertyChanged;
                    }

                    StateHasChanged();
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task CancelBtnOnClick()
        {
            try
            {
                await this.CurrentDialog.CloseAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task DeleteBtnOnClick(EventCategory category)
        {
            try
            {
                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                await dialog.CloseAsync();

                if (result.Cancelled == false)
                {
                    if (category.ObjectState == ObjectState.Added)
                    {
                        Content!.Remove(category);
                    }
                    else
                    {
                        EventCategoriesWebApiClient eventCategoriesWebApiClient = new EventCategoriesWebApiClient(httpClient, eventCategoriesWebApiClientLogger);
                        await eventCategoriesWebApiClient.DeleteEventCategory(category.EventCategoryId);
                        Content!.Remove(category);
                    }
                    StateHasChanged();
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task AddBtnOnClick()
        {
            try
            {
                var newCategory = new EventCategory
                {
                    EventCategoryId = Guid.CreateVersion7(),
                    TenantId = AuthenticatedUserData.TenantId,
                    Name = "",
                    BackColor = "#000000",
                    SortIndex = Content!.Any() ? Content!.Max(x => x.SortIndex) + 1 : 0,
                    ObjectState = ObjectState.Added,
                    UserTimeZoneId = this.userSetting!.TimeZone
                };

                newCategory.PropertyChanged += OnEventCategoryPropertyChanged;
                Content!.Add(newCategory);
                StateHasChanged();
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SaveBtnOnClick()
        {
            try
            {
                if (await ValidateData())
                {
                    EventCategoriesWebApiClient eventCategoriesWebApiClient = new EventCategoriesWebApiClient(httpClient, eventCategoriesWebApiClientLogger);

                    // Update sort indexes
                    for (int i = 0; i < Content!.Count; i++)
                    {
                        Content[i].SortIndex = i;
                    }

                    // Save each category
                    foreach (var category in Content!)
                    {
                        if (category.ObjectState != ObjectState.Unchanged)
                        {
                            if (category.ObjectState == ObjectState.Added)
                            {
                                category.DateCreatedUtc = DateTime.UtcNow;
                            }
                            category.DateModifiedUtc = DateTime.UtcNow;

                            await eventCategoriesWebApiClient.CreateOrUpdateEventCategory(category);
                        }
                    }

                    // Clears the EventCategories from cache
                    await cache.RemoveAsync(Keywords.EventCategories);

                    // Reads the EventCategories from the database
                    var updatedEventCategories = await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);

                    // Stores the EventCategories in cache
                    var eventCategoriesCacheOptions = new HybridCacheEntryOptions
                    {
                        LocalCacheExpiration = TimeSpan.FromMinutes(60)
                    };
                    await cache.SetAsync(Keywords.EventCategories, updatedEventCategories, eventCategoriesCacheOptions);

                    Content = updatedEventCategories;
                    await this.CurrentDialog.CloseAsync(Content);
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task<bool> ValidateData()
        {
            try
            {
                bool isValid = true;
                List<string> errorMessages = new List<string>();

                foreach (var category in Content!)
                {
                    var validationResult = await eventCategoryValidator.ValidateAsync(category);
                    if (!validationResult.IsValid)
                    {
                        isValid = false;
                        errorMessages.AddRange(validationResult.Errors.Select(e => e.ErrorMessage));
                    }
                }

                //Checks if the same EventCategory title exists 2 times.
                if (Content!.GroupBy(x => x.Name).Any(x => x.Count() > 1))
                {
                    isValid = false;
                    errorMessages.Add(Resources.EventCategoriesDialogResource.NameAlreadyExists);
                }

                if (!isValid)
                {
                    string errorMessage = GlobalResource.CorrectInvalidFields;
                    RenderFragment errorRF = FluentBlue.Shared.Utilities.ValidationErrorsToBulletsConverter.ConvertValidationErrorsToBullets(errorMessage, errorMessages.Distinct().ToArray(), "");

                    await dialogService.ShowDialogAsync(errorRF, new DialogParameters
                    {
                        ShowTitle = false,
                        ShowDismiss = false,
                        DialogType = DialogType.MessageBox,
                        PrimaryAction = UI.Main.GlobalResource.Close,
                        SecondaryAction = "",
                        Modal = true,
                        PreventDismissOnOverlayClick = true
                    });
                }

                return isValid;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private void HandleReorder(FluentSortableListEventArgs args)
        {
            try
            {
                if (args is null || args.OldIndex == args.NewIndex)
                {
                    return;
                }

                var oldIndex = args.OldIndex;
                var newIndex = args.NewIndex;

                var tempItems = this.Content;
                var itemToMove = this.Content![oldIndex];
                tempItems!.RemoveAt(oldIndex);

                if (newIndex < Content.Count)
                {
                    tempItems.Insert(newIndex, itemToMove);
                }
                else
                {
                    tempItems.Add(itemToMove);
                }

                // Update sort indexes
                for (int i = 0; i < tempItems!.Count; i++)
                {
                    tempItems[i].SortIndex = i;
                    if (tempItems[i].ObjectState == ObjectState.Unchanged)
                    {
                        tempItems[i].ObjectState = ObjectState.Modified;
                    }
                }

                this.Content = tempItems.OrderBy(x => x.SortIndex).ToList();

                StateHasChanged();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
    }
}
